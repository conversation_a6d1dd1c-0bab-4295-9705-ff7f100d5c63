import { useState, useEffect, useRef } from 'react'

import { useSettings } from '../../../contexts/settings-context'

export function ModelSelect() {
  const { settings, setSettings } = useSettings()
  const [showMenu, setShowMenu] = useState(false)
  const containerRef = useRef<HTMLDivElement>(null)

  // Close menu when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (containerRef.current && !containerRef.current.contains(event.target as Node)) {
        setShowMenu(false)
      }
    }

    if (showMenu) {
      document.addEventListener('mousedown', handleClickOutside)
      return () => document.removeEventListener('mousedown', handleClickOutside)
    }
  }, [showMenu])

  return (
    <div style={{ position: 'relative' }} ref={containerRef}>
      <button
        className="smtcmp-chat-input-model-select"
        onClick={() => setShowMenu(!showMenu)}
      >
        <div className="smtcmp-chat-input-model-select__model-name">
          {settings.chatModelId}
        </div>
      </button>

      {showMenu && (
        <div
          className="smtcmp-popover"
          style={{
            position: 'absolute',
            top: '100%',
            left: 0,
            zIndex: 1000,
            minWidth: '200px'
          }}
        >
          <ul>
            {settings.chatModels
              .filter(({ enable }) => enable ?? true)
              .map((chatModelOption) => (
                <li
                  key={chatModelOption.id}
                  onClick={() => {
                    setSettings({
                      ...settings,
                      chatModelId: chatModelOption.id,
                    })
                    setShowMenu(false)
                  }}
                  style={{ cursor: 'pointer' }}
                >
                  {chatModelOption.id}
                </li>
              ))}
          </ul>
        </div>
      )}
    </div>
  )
}
