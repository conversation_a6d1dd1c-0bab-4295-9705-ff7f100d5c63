import { <PERSON>Text, Trash2, Edit } from 'lucide-react'
import { useCallback, useEffect, useState, useMemo, useRef } from 'react'

import { Template } from '../../../database/json/template/types'
import { useTemplateManager } from '../../../hooks/useJsonManagers'
import { useSettings } from '../../../contexts/settings-context'
import { SerializedLexicalNode } from 'lexical'

// Helper function to convert template content to plain text
function templateContentToPlainText(content: { nodes: SerializedLexicalNode[] }): string {
  return content.nodes.map(lexicalNodeToPlainText).join('')
}

function lexicalNodeToPlainText(node: SerializedLexicalNode): string {
  if ('children' in node) {
    // Process children recursively and join their results
    return (node.children as SerializedLexicalNode[])
      .map(lexicalNodeToPlainText)
      .join('')
  } else if (node.type === 'linebreak') {
    return '\n'
  } else if ('text' in node && typeof node.text === 'string') {
    return node.text
  }
  return ''
}

export function TemplateSelectButton({
  onOpenEditDialog
}: {
  onOpenEditDialog: (template: Template) => void
}) {
  const templateManager = useTemplateManager()
  const { settings, setSettings } = useSettings()
  const [showMenu, setShowMenu] = useState(false)
  const [templates, setTemplates] = useState<Template[]>([])
  const containerRef = useRef<HTMLDivElement>(null)

  // Load all templates when component mounts
  useEffect(() => {
    templateManager.searchTemplates('').then(setTemplates)
  }, [templateManager])

  // Refresh templates when menu opens (to get latest changes)
  useEffect(() => {
    if (showMenu) {
      templateManager.searchTemplates('').then(setTemplates)
    }
  }, [showMenu, templateManager])

  // Close menu when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (containerRef.current && !containerRef.current.contains(event.target as Node)) {
        setShowMenu(false)
      }
    }

    if (showMenu) {
      document.addEventListener('mousedown', handleClickOutside)
      return () => document.removeEventListener('mousedown', handleClickOutside)
    }
  }, [showMenu])

  // Find current template based on system prompt
  const currentTemplate = useMemo(() => {
    if (!settings.systemPrompt.trim()) return null

    return templates.find(template => {
      const templateText = templateContentToPlainText(template.content).trim()
      return templateText === settings.systemPrompt.trim()
    })
  }, [templates, settings.systemPrompt])

  const handleTemplateSelect = useCallback(
    async (template: Template) => {
      const templateText = templateContentToPlainText(template.content).trim()

      await setSettings({
        ...settings,
        systemPrompt: templateText,
      })

      setShowMenu(false)
    },
    [settings, setSettings],
  )

  // Clear system prompt (set to empty)
  const handleClearSystemPrompt = useCallback(
    async () => {
      await setSettings({
        ...settings,
        systemPrompt: '',
      })

      setShowMenu(false)
    },
    [settings, setSettings],
  )

  // Delete template
  const handleDeleteTemplate = useCallback(
    async (template: Template) => {
      await templateManager.deleteTemplate(template.id)
      // Refresh templates list
      const updatedTemplates = await templateManager.searchTemplates('')
      setTemplates(updatedTemplates)

      // If the deleted template was currently selected, clear the system prompt
      if (currentTemplate?.id === template.id) {
        await setSettings({
          ...settings,
          systemPrompt: '',
        })
      }
    },
    [templateManager, currentTemplate, settings, setSettings],
  )

  // Edit template
  const handleEditTemplate = useCallback(
    (template: Template) => {
      setShowMenu(false)
      // Use setTimeout to ensure menu closes before opening dialog
      setTimeout(() => {
        onOpenEditDialog(template)
      }, 100)
    },
    [onOpenEditDialog],
  )

  // Display current template name or "Custom" if no match
  const displayText = currentTemplate ? currentTemplate.name : (settings.systemPrompt.trim() ? 'Custom' : 'System Prompt')

  return (
    <div className="smtcmp-template-select-container" style={{ position: 'relative' }} ref={containerRef}>
      <button
        className="smtcmp-chat-input-template-select"
        onClick={() => setShowMenu(!showMenu)}
      >
        <div className="smtcmp-chat-input-template-select__icon">
          <FileText size={12} />
        </div>
        <div className="smtcmp-chat-input-template-select__text">{displayText}</div>
      </button>

      {showMenu && (
        <div
          className="smtcmp-popover"
          style={{
            position: 'absolute',
            top: '100%',
            left: 0,
            zIndex: 1000,
            minWidth: '200px'
          }}
        >
          <ul>
            {/* Clear system prompt option */}
            <li
              style={{
                fontStyle: 'italic',
                color: settings.systemPrompt.trim() ? 'var(--text-normal)' : 'var(--text-muted)',
                cursor: 'pointer'
              }}
              onClick={handleClearSystemPrompt}
            >
              {settings.systemPrompt.trim() ? '✓ ' : ''}None
            </li>

            {templates.length === 0 ? (
              <li
                style={{
                  padding: '8px 12px',
                  color: 'var(--text-muted)',
                  fontSize: 'var(--font-ui-small)',
                }}
              >
                No templates found
              </li>
            ) : (
              templates.map((template) => {
                const isSelected = currentTemplate?.id === template.id
                return (
                  <li
                    key={template.id}
                    style={{
                      color: isSelected ? 'var(--text-accent)' : 'var(--text-normal)'
                    }}
                  >
                    <div className="smtcmp-template-menu-item">
                      <div
                        className="text"
                        onClick={() => handleTemplateSelect(template)}
                        style={{ cursor: 'pointer' }}
                      >
                        {isSelected ? '✓ ' : ''}{template.name}
                      </div>
                      <div style={{ display: 'flex', gap: 'var(--size-4-1)' }}>
                        <div
                          className="smtcmp-template-menu-item-delete"
                          onClick={(evt) => {
                            evt.stopPropagation()
                            evt.preventDefault()
                            handleEditTemplate(template)
                          }}
                          style={{ cursor: 'pointer' }}
                        >
                          <Edit size={12} />
                        </div>
                        <div
                          className="smtcmp-template-menu-item-delete"
                          onClick={(evt) => {
                            evt.stopPropagation()
                            evt.preventDefault()
                            handleDeleteTemplate(template)
                          }}
                          style={{ cursor: 'pointer' }}
                        >
                          <Trash2 size={12} />
                        </div>
                      </div>
                    </div>
                  </li>
                )
              })
            )}
          </ul>
        </div>
      )}
    </div>
  )
}
