import { ChevronDown } from 'lucide-react'
import { useState, useEffect, useRef } from 'react'

type SplitButtonProps = {
  primaryText: string
  menuOptions: {
    label: string
    onClick: () => void
  }[]
  onPrimaryClick: () => void
}

export function SplitButton({
  primaryText,
  menuOptions,
  onPrimaryClick,
}: SplitButtonProps) {
  const [showMenu, setShowMenu] = useState(false)
  const containerRef = useRef<HTMLDivElement>(null)

  // Close menu when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (containerRef.current && !containerRef.current.contains(event.target as Node)) {
        setShowMenu(false)
      }
    }

    if (showMenu) {
      document.addEventListener('mousedown', handleClickOutside)
      return () => document.removeEventListener('mousedown', handleClickOutside)
    }
  }, [showMenu])

  return (
    <div className="smtcmp-split-button" ref={containerRef} style={{ position: 'relative' }}>
      <button onClick={onPrimaryClick} className="smtcmp-split-button-primary">
        {primaryText}
      </button>
      <button
        className="smtcmp-split-button-toggle"
        aria-label="Show more options"
        onClick={() => setShowMenu(!showMenu)}
      >
        <ChevronDown size={16} />
      </button>

      {showMenu && (
        <div
          className="smtcmp-popover"
          style={{
            position: 'absolute',
            top: '100%',
            right: 0,
            zIndex: 1000,
            minWidth: '150px'
          }}
        >
          <ul>
            {menuOptions.map((option) => (
              <li
                key={option.label}
                onClick={() => {
                  option.onClick()
                  setShowMenu(false)
                }}
                style={{ cursor: 'pointer' }}
              >
                {option.label}
              </li>
            ))}
          </ul>
        </div>
      )}
    </div>
  )
}
